{"name": "labsyncpro-server", "version": "1.0.0", "description": "Backend server for LabSyncPro", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.4", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["laboratory", "management", "api", "education"], "author": "LabSyncPro Team", "license": "MIT"}