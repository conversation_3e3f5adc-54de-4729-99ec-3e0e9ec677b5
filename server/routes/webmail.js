const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { query } = require('../config/database');
const {
  getEmails,
  getEmail,
  markAsRead,
  sendInternalEmail,
  deleteEmail,
  getMailboxStats,
  storeEmail
} = require('../services/mailboxService');

const router = express.Router();

// Get inbox emails
router.get('/inbox', authenticateToken, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const emails = await getEmails(req.user.id, 'inbox', parseInt(limit));
    res.json({ emails });
  } catch (error) {
    console.error('Inbox fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch emails' });
  }
});

// Get emails from specific folder
router.get('/folder/:folder', authenticateToken, async (req, res) => {
  try {
    const { folder } = req.params;
    const { limit = 50 } = req.query;

    const validFolders = ['inbox', 'sent', 'drafts', 'trash'];
    if (!validFolders.includes(folder)) {
      return res.status(400).json({ error: 'Invalid folder' });
    }

    const emails = await getEmails(req.user.id, folder, parseInt(limit));
    res.json({ emails });
  } catch (error) {
    console.error('Folder fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch emails' });
  }
});

// Get specific email
router.get('/email/:emailId', authenticateToken, async (req, res) => {
  try {
    const { emailId } = req.params;
    const email = await getEmail(req.user.id, emailId);

    if (!email) {
      return res.status(404).json({ error: 'Email not found' });
    }

    // Mark as read when viewing
    await markAsRead(req.user.id, emailId);

    res.json({ email });
  } catch (error) {
    console.error('Email fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch email' });
  }
});

// Send email
router.post('/send', authenticateToken, async (req, res) => {
  try {
    const { to, subject, content } = req.body;

    if (!to || !subject || !content) {
      return res.status(400).json({ error: 'Missing required fields: to, subject, content' });
    }

    const result = await sendInternalEmail(req.user.id, to, subject, content);

    res.json({
      message: 'Email sent successfully',
      messageId: result.inboxId
    });

  } catch (error) {
    console.error('Send email error:', error);
    res.status(500).json({ error: error.message || 'Failed to send email' });
  }
});

// Get mailbox stats
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await getMailboxStats(req.user.id);
    res.json({ stats });
  } catch (error) {
    console.error('Stats fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch mailbox stats' });
  }
});

// Mark email as read
router.put('/email/:emailId/read', authenticateToken, async (req, res) => {
  try {
    const { emailId } = req.params;
    const success = await markAsRead(req.user.id, emailId);

    if (success) {
      res.json({ message: 'Email marked as read' });
    } else {
      res.status(404).json({ error: 'Email not found' });
    }
  } catch (error) {
    console.error('Mark read error:', error);
    res.status(500).json({ error: 'Failed to mark email as read' });
  }
});

// Delete email
router.delete('/email/:emailId', authenticateToken, async (req, res) => {
  try {
    const { emailId } = req.params;
    const success = await deleteEmail(req.user.id, emailId);

    if (success) {
      res.json({ message: 'Email deleted successfully' });
    } else {
      res.status(404).json({ error: 'Email not found' });
    }
  } catch (error) {
    console.error('Delete email error:', error);
    res.status(500).json({ error: 'Failed to delete email' });
  }
});

// Get all users for email composition
router.get('/users', authenticateToken, async (req, res) => {
  try {
    const result = await query(
      'SELECT email, first_name, last_name, role FROM users WHERE is_active = true ORDER BY first_name, last_name',
      []
    );

    const users = result.rows.map(user => ({
      email: user.email,
      name: `${user.first_name} ${user.last_name}`,
      role: user.role
    }));

    res.json({ users });
  } catch (error) {
    console.error('Users fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

module.exports = router;
