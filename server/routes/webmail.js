const express = require('express');
const { ImapFlow } = require('imapflow');
const nodemailer = require('nodemailer');
const { authenticateToken } = require('../middleware/auth');
const { query } = require('../config/database');

const router = express.Router();

// Get user's mail address from database
const getUserMailAddress = async (userId) => {
  const result = await query(
    'SELECT mail_address FROM users WHERE id = $1',
    [userId]
  );
  return result.rows[0]?.mail_address;
};

// Get user's mail credentials (for demo - in production, store encrypted)
const getMailCredentials = async (mailAddress) => {
  // For demo purposes, we'll use a simple password
  // In production, you'd store this securely
  return {
    user: mailAddress,
    pass: 'temp123' // This should be retrieved from secure storage
  };
};

// Get inbox emails
router.get('/inbox', authenticateToken, async (req, res) => {
  try {
    const mailAddress = await getUserMailAddress(req.user.id);
    if (!mailAddress) {
      return res.status(404).json({ error: 'Mail address not found' });
    }

    const credentials = await getMailCredentials(mailAddress);
    
    const client = new ImapFlow({
      host: 'localhost',
      port: 143,
      secure: false,
      auth: credentials,
      logger: false
    });

    await client.connect();
    
    // Select INBOX
    let lock = await client.getMailboxLock('INBOX');
    
    try {
      // Get recent emails
      const messages = [];
      for await (let message of client.fetch('1:*', {
        envelope: true,
        bodyStructure: true,
        source: true
      })) {
        messages.push({
          uid: message.uid,
          subject: message.envelope.subject,
          from: message.envelope.from?.[0],
          date: message.envelope.date,
          flags: message.flags,
          size: message.size
        });
      }
      
      // Sort by date (newest first)
      messages.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      res.json({ emails: messages.slice(0, 50) }); // Limit to 50 recent emails
      
    } finally {
      lock.release();
    }
    
    await client.logout();
    
  } catch (error) {
    console.error('Inbox fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch emails' });
  }
});

// Get specific email
router.get('/email/:uid', authenticateToken, async (req, res) => {
  try {
    const { uid } = req.params;
    const mailAddress = await getUserMailAddress(req.user.id);
    if (!mailAddress) {
      return res.status(404).json({ error: 'Mail address not found' });
    }

    const credentials = await getMailCredentials(mailAddress);
    
    const client = new ImapFlow({
      host: 'localhost',
      port: 143,
      secure: false,
      auth: credentials,
      logger: false
    });

    await client.connect();
    
    let lock = await client.getMailboxLock('INBOX');
    
    try {
      const message = await client.fetchOne(uid, {
        envelope: true,
        bodyStructure: true,
        source: true
      });
      
      if (!message) {
        return res.status(404).json({ error: 'Email not found' });
      }
      
      // Parse email content
      const emailData = {
        uid: message.uid,
        subject: message.envelope.subject,
        from: message.envelope.from?.[0],
        to: message.envelope.to,
        date: message.envelope.date,
        flags: message.flags,
        content: message.source.toString() // Raw email content
      };
      
      res.json({ email: emailData });
      
    } finally {
      lock.release();
    }
    
    await client.logout();
    
  } catch (error) {
    console.error('Email fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch email' });
  }
});

// Send email
router.post('/send', authenticateToken, async (req, res) => {
  try {
    const { to, subject, text, html } = req.body;
    const mailAddress = await getUserMailAddress(req.user.id);
    
    if (!mailAddress) {
      return res.status(404).json({ error: 'Mail address not found' });
    }

    const credentials = await getMailCredentials(mailAddress);
    
    const transporter = nodemailer.createTransport({
      host: 'localhost',
      port: 25,
      secure: false,
      auth: credentials
    });

    const mailOptions = {
      from: mailAddress,
      to: to,
      subject: subject,
      text: text,
      html: html
    };

    const info = await transporter.sendMail(mailOptions);
    
    res.json({ 
      message: 'Email sent successfully',
      messageId: info.messageId 
    });
    
  } catch (error) {
    console.error('Send email error:', error);
    res.status(500).json({ error: 'Failed to send email' });
  }
});

// Get mail folders
router.get('/folders', authenticateToken, async (req, res) => {
  try {
    const mailAddress = await getUserMailAddress(req.user.id);
    if (!mailAddress) {
      return res.status(404).json({ error: 'Mail address not found' });
    }

    const credentials = await getMailCredentials(mailAddress);
    
    const client = new ImapFlow({
      host: 'localhost',
      port: 143,
      secure: false,
      auth: credentials,
      logger: false
    });

    await client.connect();
    
    const folders = await client.list();
    
    await client.logout();
    
    res.json({ folders: folders });
    
  } catch (error) {
    console.error('Folders fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch folders' });
  }
});

// Mark email as read/unread
router.put('/email/:uid/flags', authenticateToken, async (req, res) => {
  try {
    const { uid } = req.params;
    const { flags } = req.body; // e.g., ['\\Seen'] for read
    
    const mailAddress = await getUserMailAddress(req.user.id);
    if (!mailAddress) {
      return res.status(404).json({ error: 'Mail address not found' });
    }

    const credentials = await getMailCredentials(mailAddress);
    
    const client = new ImapFlow({
      host: 'localhost',
      port: 143,
      secure: false,
      auth: credentials,
      logger: false
    });

    await client.connect();
    
    let lock = await client.getMailboxLock('INBOX');
    
    try {
      await client.messageFlagsAdd(uid, flags);
      res.json({ message: 'Flags updated successfully' });
    } finally {
      lock.release();
    }
    
    await client.logout();
    
  } catch (error) {
    console.error('Flag update error:', error);
    res.status(500).json({ error: 'Failed to update flags' });
  }
});

module.exports = router;
