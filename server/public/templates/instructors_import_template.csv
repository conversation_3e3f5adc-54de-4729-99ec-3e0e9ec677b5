first_name,last_name,email,employee_id,department,password,phone,office_location
Dr. <PERSON>,<PERSON>,<EMAIL>,EMP001,Computer Science,instructor123,******-0101,CS Building Room 201
Prof. <PERSON>,<PERSON>,<EMAIL>,EMP002,Computer Science,instructor123,******-0102,CS Building Room 203
<PERSON><PERSON>,<PERSON>,micha<PERSON>.<EMAIL>,EMP003,Computer Science,instructor123,******-0103,CS Building Room 205
Prof. <PERSON>,<PERSON>,<EMAIL>,EMP004,Information Technology,instructor123,******-0104,IT Building Room 301
Dr. <PERSON>,<PERSON>,<EMAIL>,EMP005,Information Technology,instructor123,******-0105,IT Building Room 303
Prof. <PERSON>,<PERSON>,<EMAIL>,EMP006,Networking,instructor123,******-0106,Network Lab Building Room 101
Dr. <PERSON>,<PERSON>,<EMAIL>,EMP007,Networking,instructor123,******-0107,Network Lab Building Room 103
Prof<PERSON>,<PERSON>,<EMAIL>,EMP008,<PERSON> Science,instructor123,******-0108,CS Building Room 207
Dr. <PERSON>,<PERSON>,<EMAIL>,EMP009,Information Technology,instructor123,******-0109,IT Building Room 305
Prof. Amanda,<PERSON>,<EMAIL>,EMP010,Computer Science,instructor123,******-0110,CS Building Room 209
