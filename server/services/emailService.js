const nodemailer = require('nodemailer');

// Email configuration
const emailConfig = {
  // For MailHog (development)
  development: {
    host: 'localhost',
    port: 1025,
    secure: false,
    auth: false
  },
  
  // For Postfix (production)
  production: {
    host: 'localhost', // or your mail server IP
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'your-password'
    }
  },
  
  // For external SMTP (Gmail, etc.)
  external: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  }
};

// Create transporter based on environment
const createTransporter = () => {
  const env = process.env.NODE_ENV || 'development';
  const config = emailConfig[env] || emailConfig.development;

  return nodemailer.createTransport(config);
};

// Send password reset email
const sendPasswordResetEmail = async (email, resetToken, firstName) => {
  const transporter = createTransporter();
  
  const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/password-reset?token=${resetToken}`;
  
  const mailOptions = {
    from: process.env.FROM_EMAIL || '<EMAIL>',
    to: email,
    subject: 'LabSyncPro - Password Reset Request',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>LabSyncPro</h1>
            <h2>Password Reset Request</h2>
          </div>
          <div class="content">
            <p>Hello ${firstName},</p>
            <p>We received a request to reset your password for your LabSyncPro account.</p>
            <p>Click the button below to reset your password:</p>
            <a href="${resetUrl}" class="button">Reset Password</a>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">${resetUrl}</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
            <p>If you didn't request this password reset, please ignore this email.</p>
            <p>Best regards,<br>The LabSyncPro Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Password reset email sent:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return { success: false, error: error.message };
  }
};

// Send welcome email for new users
const sendWelcomeEmail = async (email, firstName, tempPassword) => {
  const transporter = createTransporter();
  
  const loginUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/login`;
  
  const mailOptions = {
    from: process.env.FROM_EMAIL || '<EMAIL>',
    to: email,
    subject: 'Welcome to LabSyncPro',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .credentials { background: #e8f4f8; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>LabSyncPro</h1>
            <h2>Welcome to LabSyncPro!</h2>
          </div>
          <div class="content">
            <p>Hello ${firstName},</p>
            <p>Your LabSyncPro account has been created successfully!</p>
            <div class="credentials">
              <h3>Your Login Credentials:</h3>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> ${tempPassword}</p>
            </div>
            <p><strong>Important:</strong> Please change your password after your first login for security.</p>
            <a href="${loginUrl}" class="button">Login to LabSyncPro</a>
            <p>If you have any questions, please contact your administrator.</p>
            <p>Best regards,<br>The LabSyncPro Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Welcome email sent:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return { success: false, error: error.message };
  }
};

// Test email connection
const testEmailConnection = async () => {
  const transporter = createTransporter();

  try {
    await transporter.verify();
    console.log('Email server connection successful');
    return { success: true };
  } catch (error) {
    console.error('Email server connection failed:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendPasswordResetEmail,
  sendWelcomeEmail,
  testEmailConnection
};
