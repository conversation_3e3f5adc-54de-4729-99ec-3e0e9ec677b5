import React, { useState, useEffect } from 'react';
import { 
  Mail, 
  Send, 
  Inbox, 
  Sent, 
  Trash, 
  Edit3, 
  Users, 
  Refresh<PERSON><PERSON>,
  Eye,
  EyeOff,
  X,
  Plus
} from 'lucide-react';

interface Email {
  id: string;
  from: {
    email: string;
    name: string;
  };
  to: {
    email: string;
    name: string;
  };
  subject: string;
  content: string;
  timestamp: string;
  read: boolean;
  type: string;
}

interface MailboxStats {
  inbox: { total: number; unread: number };
  sent: { total: number; unread: number };
  drafts: { total: number; unread: number };
  trash: { total: number; unread: number };
}

interface User {
  email: string;
  name: string;
  role: string;
}

const Webmail: React.FC = () => {
  const [emails, setEmails] = useState<Email[]>([]);
  const [currentFolder, setCurrentFolder] = useState('inbox');
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [showCompose, setShowCompose] = useState(false);
  const [stats, setStats] = useState<MailboxStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  // Compose form state
  const [composeForm, setComposeForm] = useState({
    to: '',
    subject: '',
    content: ''
  });

  const token = localStorage.getItem('token');

  const fetchEmails = async (folder: string = 'inbox') => {
    setLoading(true);
    try {
      const endpoint = folder === 'inbox' 
        ? '/api/webmail/inbox' 
        : `/api/webmail/folder/${folder}`;
      
      const response = await fetch(`http://localhost:5002${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setEmails(data.emails || []);
      }
    } catch (error) {
      console.error('Error fetching emails:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:5002/api/webmail/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('http://localhost:5002/api/webmail/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const sendEmail = async () => {
    if (!composeForm.to || !composeForm.subject || !composeForm.content) {
      alert('Please fill in all fields');
      return;
    }

    try {
      const response = await fetch('http://localhost:5002/api/webmail/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(composeForm)
      });

      if (response.ok) {
        alert('Email sent successfully!');
        setShowCompose(false);
        setComposeForm({ to: '', subject: '', content: '' });
        fetchEmails(currentFolder);
        fetchStats();
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error sending email:', error);
      alert('Failed to send email');
    }
  };

  const markAsRead = async (emailId: string) => {
    try {
      await fetch(`http://localhost:5002/api/webmail/email/${emailId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // Update local state
      setEmails(emails.map(email => 
        email.id === emailId ? { ...email, read: true } : email
      ));
      fetchStats();
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  };

  const deleteEmail = async (emailId: string) => {
    if (!confirm('Are you sure you want to delete this email?')) return;

    try {
      const response = await fetch(`http://localhost:5002/api/webmail/email/${emailId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setEmails(emails.filter(email => email.id !== emailId));
        setSelectedEmail(null);
        fetchStats();
      }
    } catch (error) {
      console.error('Error deleting email:', error);
    }
  };

  const openEmail = async (email: Email) => {
    setSelectedEmail(email);
    if (!email.read) {
      await markAsRead(email.id);
    }
  };

  useEffect(() => {
    fetchEmails(currentFolder);
    fetchStats();
    fetchUsers();
  }, [currentFolder]);

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const folders = [
    { id: 'inbox', name: 'Inbox', icon: Inbox, count: stats?.inbox.unread || 0 },
    { id: 'sent', name: 'Sent', icon: Send, count: 0 },
    { id: 'drafts', name: 'Drafts', icon: Edit3, count: 0 },
    { id: 'trash', name: 'Trash', icon: Trash, count: 0 }
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-4 border-b">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <Mail className="mr-2" size={24} />
            Webmail
          </h2>
        </div>
        
        <div className="p-4">
          <button
            onClick={() => setShowCompose(true)}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center"
          >
            <Plus className="mr-2" size={16} />
            Compose
          </button>
        </div>

        <div className="px-4">
          {folders.map(folder => (
            <button
              key={folder.id}
              onClick={() => setCurrentFolder(folder.id)}
              className={`w-full text-left p-3 rounded-lg mb-2 flex items-center justify-between ${
                currentFolder === folder.id 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <folder.icon className="mr-3" size={18} />
                {folder.name}
              </div>
              {folder.count > 0 && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {folder.count}
                </span>
              )}
            </button>
          ))}
        </div>

        <div className="p-4 border-t mt-4">
          <button
            onClick={() => {
              fetchEmails(currentFolder);
              fetchStats();
            }}
            className="w-full text-gray-600 hover:text-gray-800 flex items-center justify-center"
          >
            <RefreshCw className="mr-2" size={16} />
            Refresh
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Email List */}
        <div className="w-1/2 bg-white border-r">
          <div className="p-4 border-b">
            <h3 className="text-lg font-semibold capitalize">{currentFolder}</h3>
            {stats && (
              <p className="text-sm text-gray-600">
                {stats[currentFolder as keyof MailboxStats]?.total || 0} emails
                {stats[currentFolder as keyof MailboxStats]?.unread > 0 && 
                  ` (${stats[currentFolder as keyof MailboxStats]?.unread} unread)`
                }
              </p>
            )}
          </div>

          <div className="overflow-y-auto h-full">
            {loading ? (
              <div className="p-4 text-center">Loading...</div>
            ) : emails.length === 0 ? (
              <div className="p-4 text-center text-gray-500">No emails in {currentFolder}</div>
            ) : (
              emails.map(email => (
                <div
                  key={email.id}
                  onClick={() => openEmail(email)}
                  className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                    !email.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  } ${selectedEmail?.id === email.id ? 'bg-blue-100' : ''}`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-semibold text-gray-800">
                      {currentFolder === 'sent' ? email.to.name : email.from.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatDate(email.timestamp)}
                    </div>
                  </div>
                  <div className="font-medium text-gray-700 mb-1">{email.subject}</div>
                  <div className="text-sm text-gray-600 truncate">
                    {email.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      email.type === 'system' ? 'bg-green-100 text-green-700' :
                      email.type === 'notification' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {email.type}
                    </span>
                    {!email.read && (
                      <EyeOff className="text-blue-500" size={16} />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Email Content */}
        <div className="w-1/2 bg-white">
          {selectedEmail ? (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold">{selectedEmail.subject}</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => deleteEmail(selectedEmail.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash size={18} />
                    </button>
                    <button
                      onClick={() => setSelectedEmail(null)}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      <X size={18} />
                    </button>
                  </div>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  <strong>From:</strong> {selectedEmail.from.name} ({selectedEmail.from.email})
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  <strong>To:</strong> {selectedEmail.to.name} ({selectedEmail.to.email})
                </div>
                <div className="text-sm text-gray-600">
                  <strong>Date:</strong> {formatDate(selectedEmail.timestamp)}
                </div>
              </div>
              <div className="flex-1 p-4 overflow-y-auto">
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: selectedEmail.content }}
                />
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Mail size={64} className="mx-auto mb-4 text-gray-300" />
                <p>Select an email to read</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Compose Modal */}
      {showCompose && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl mx-4">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold">Compose Email</h3>
              <button
                onClick={() => setShowCompose(false)}
                className="text-gray-600 hover:text-gray-800"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">To:</label>
                <select
                  value={composeForm.to}
                  onChange={(e) => setComposeForm({...composeForm, to: e.target.value})}
                  className="w-full border rounded-lg px-3 py-2"
                >
                  <option value="">Select recipient...</option>
                  {users.map(user => (
                    <option key={user.email} value={user.email}>
                      {user.name} ({user.email}) - {user.role}
                    </option>
                  ))}
                </select>
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Subject:</label>
                <input
                  type="text"
                  value={composeForm.subject}
                  onChange={(e) => setComposeForm({...composeForm, subject: e.target.value})}
                  className="w-full border rounded-lg px-3 py-2"
                  placeholder="Enter subject..."
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Message:</label>
                <textarea
                  value={composeForm.content}
                  onChange={(e) => setComposeForm({...composeForm, content: e.target.value})}
                  className="w-full border rounded-lg px-3 py-2 h-40"
                  placeholder="Enter your message..."
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowCompose(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={sendEmail}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                >
                  <Send className="mr-2" size={16} />
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Webmail;
