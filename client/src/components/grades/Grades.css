.grades {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.grades-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.grades-header .header-content {
  text-align: left;
}

.grades-header .header-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.grades-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.grades-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.grades-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.grades-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #ecf0f1;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #7f8c8d;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.grades-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.grade-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #ecf0f1;
}

.grade-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.grade-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.student-info h3 {
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.student-id {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
}

.grade-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assignment-info {
  margin-bottom: 1rem;
}

.assignment-info h4 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.assignment-details {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.submission-summary {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.submission-summary > div {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #5a6c7d;
}

.submission-summary > div:last-child {
  margin-bottom: 0;
}

.submission-type {
  font-weight: 600;
  text-transform: capitalize;
}

.grade-display {
  background: #f1f2f6;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.score-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.score {
  font-size: 1.5rem;
  font-weight: 700;
}

.percentage {
  color: #7f8c8d;
  font-weight: 600;
}

.feedback-preview {
  color: #5a6c7d;
  font-style: italic;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.graded-info {
  color: #7f8c8d;
  font-size: 0.85rem;
}

.grade-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.btn-outline {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.no-grades {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-grades h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grades {
    padding: 1rem;
  }
  
  .grades-header h1 {
    font-size: 2rem;
  }
  
  .grades-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-tabs {
    flex-direction: column;
  }
  
  .filter-tab {
    text-align: center;
  }
  
  .grade-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .grade-status {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .grades {
    padding: 0.5rem;
  }
  
  .grade-card {
    padding: 1rem;
  }
  
  .grades-controls {
    padding: 1rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.grading-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.modal-content {
  padding: 1.5rem;
}

.submission-info {
  margin-bottom: 2rem;
}

.submission-info h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 600;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.info-item span {
  color: #2c3e50;
  font-weight: 500;
}

.submission-files {
  margin-bottom: 2rem;
}

.submission-files h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
}

.file-name {
  color: #2c3e50;
  font-weight: 500;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.submission-text {
  margin-bottom: 2rem;
}

.submission-text h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.text-content {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  color: #2c3e50;
  line-height: 1.6;
  white-space: pre-wrap;
}

.grading-section {
  background: #f1f2f6;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.score-input {
  margin-bottom: 1.5rem;
}

.score-input label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.score-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.score-field {
  width: 100px;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
}

.score-field:focus {
  outline: none;
  border-color: #3498db;
}

.max-score {
  color: #7f8c8d;
  font-weight: 600;
  font-size: 1.1rem;
}

.score-percentage {
  font-weight: 700;
  font-size: 1.2rem;
}

.feedback-input label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.feedback-field {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
}

.feedback-field:focus {
  outline: none;
  border-color: #3498db;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.btn-primary {
  background: #3498db;
  color: white;
  border: 2px solid #3498db;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
  border-color: #2980b9;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: #7f8c8d;
  border: 2px solid #ecf0f1;
}

.btn-secondary:hover {
  background: #ecf0f1;
  color: #2c3e50;
}
