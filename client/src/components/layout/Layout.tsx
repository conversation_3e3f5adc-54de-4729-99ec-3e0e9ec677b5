import React, { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Layout.css';

const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const menuItems = [
    { path: '/dashboard', label: 'Dashboard', icon: '📊' },
    ...(user?.role === 'student'
      ? [
          { path: '/my-submissions', label: 'My Assignments', icon: '📋' },
          { path: '/groups', label: 'Groups', icon: '👥' },
          { path: '/grades', label: 'My Grades', icon: '📈' },
        ]
      : [
          { path: '/labs', label: 'Labs', icon: '🖥️' },
          { path: '/schedules', label: 'Schedules', icon: '📅' },
          { path: '/assignment-creation', label: 'Create', icon: '📝' },
          { path: '/assignment-management', label: 'Assign', icon: '📋' },
          { path: '/submissions', label: 'Submissions', icon: '📄' },
          { path: '/grades', label: 'Grades', icon: '📈' },
          { path: '/groups', label: 'Groups', icon: '👥' },
          { path: '/capacity', label: 'Capacity', icon: '🪑' },
          ...(user?.role === 'admin' || user?.role === 'instructor'
            ? [{ path: '/users', label: 'Users', icon: '👤' }]
            : []
          ),
          ...(user?.role === 'admin'
            ? [
                { path: '/data-import', label: 'Import', icon: '📊' },
                { path: '/password-reset-requests', label: 'Resets', icon: '🔐' }
              ]
            : []
          ),
          ...(user?.role === 'admin' || user?.role === 'instructor'
            ? [{ path: '/data-export', label: 'Export', icon: '📤' }]
            : []
          ),
        ]
    ),
    { path: '/webmail', label: 'Webmail', icon: '📧' },
  ];

  return (
    <div className="layout">
      {/* Header */}
      <header className="header">
        <div className="header-left">
          <button className="sidebar-toggle" onClick={toggleSidebar}>
            ☰
          </button>
          <h1 className="logo">LabSyncPro</h1>

          {/* Desktop Navigation */}
          <nav className="desktop-nav">
            {menuItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`desktop-nav-item ${location.pathname === item.path ? 'active' : ''}`}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </Link>
            ))}
          </nav>
        </div>

        <div className="header-right">
          <div className="user-info">
            <span className="user-name">
              {user?.firstName} {user?.lastName}
            </span>
            <span className="user-role">{user?.role}</span>
          </div>

          <div className="user-menu">
            <Link to="/profile" className="profile-link">
              Profile
            </Link>
            <button onClick={handleLogout} className="logout-button">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Sidebar */}
      <aside className={`sidebar ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <nav className="sidebar-nav">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}
              onClick={() => setSidebarOpen(false)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </Link>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main className={`main-content ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <div className="content-wrapper">
          <Outlet />
        </div>
      </main>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div className="sidebar-overlay" onClick={() => setSidebarOpen(false)} />
      )}
    </div>
  );
};

export default Layout;
