import React, { useState, useRef, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Layout.css';

const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [useDoubleRowNav, setUseDoubleRowNav] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const navRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Handle horizontal scrolling for navigation
  useEffect(() => {
    const nav = navRef.current;
    if (!nav) return;

    const handleWheel = (e: WheelEvent) => {
      // Only handle horizontal scrolling when content overflows
      if (nav.scrollWidth > nav.clientWidth) {
        e.preventDefault();
        nav.scrollLeft += e.deltaY;
      }
    };

    nav.addEventListener('wheel', handleWheel, { passive: false });
    return () => nav.removeEventListener('wheel', handleWheel);
  }, []);

  // Split menu items into two rows for double-row layout
  const splitMenuItems = () => {
    const midpoint = Math.ceil(menuItems.length / 2);
    return {
      firstRow: menuItems.slice(0, midpoint),
      secondRow: menuItems.slice(midpoint)
    };
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const menuItems = [
    { path: '/dashboard', label: 'Dashboard', icon: '📊' },
    ...(user?.role === 'student'
      ? [
          { path: '/my-submissions', label: 'My Assignments', icon: '📋' },
          { path: '/groups', label: 'Groups', icon: '👥' },
          { path: '/grades', label: 'My Grades', icon: '📈' },
        ]
      : [
          { path: '/labs', label: 'Labs', icon: '🖥️' },
          { path: '/schedules', label: 'Schedules', icon: '📅' },
          { path: '/assignment-creation', label: 'Assignment Creation', icon: '📝' },
          { path: '/assignment-management', label: 'Assignment Management', icon: '📋' },
          { path: '/submissions', label: 'Submissions', icon: '📄' },
          { path: '/grades', label: 'Grades', icon: '📈' },
          { path: '/groups', label: 'Groups', icon: '👥' },
          { path: '/capacity', label: 'Capacity Planning', icon: '🪑' },
          ...(user?.role === 'admin' || user?.role === 'instructor'
            ? [{ path: '/users', label: 'Users', icon: '👤' }]
            : []
          ),
          ...(user?.role === 'admin'
            ? [
                { path: '/data-import', label: 'Data Import', icon: '📊' },
                { path: '/password-reset-requests', label: 'Password Requests', icon: '🔐' }
              ]
            : []
          ),
          ...(user?.role === 'admin' || user?.role === 'instructor'
            ? [{ path: '/data-export', label: 'Data Export', icon: '📤' }]
            : []
          ),
        ]
    ),
    { path: '/webmail', label: 'Webmail', icon: '📧' },
  ];

  return (
    <div className="layout">
      {/* Header */}
      <header className="header">
        <div className="header-left">
          <button className="sidebar-toggle" onClick={toggleSidebar}>
            ☰
          </button>
          <h1 className="logo">LabSyncPro</h1>

          {/* Desktop Navigation */}
          <div className="nav-wrapper">
            {useDoubleRowNav ? (
              // Double-row navigation
              <div className="desktop-nav-double">
                <nav className="desktop-nav-row">
                  {splitMenuItems().firstRow.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className={`desktop-nav-item ${location.pathname === item.path ? 'active' : ''}`}
                    >
                      <span className="nav-icon">{item.icon}</span>
                      <span className="nav-label">{item.label}</span>
                    </Link>
                  ))}
                </nav>
                <nav className="desktop-nav-row">
                  {splitMenuItems().secondRow.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className={`desktop-nav-item ${location.pathname === item.path ? 'active' : ''}`}
                    >
                      <span className="nav-icon">{item.icon}</span>
                      <span className="nav-label">{item.label}</span>
                    </Link>
                  ))}
                </nav>
              </div>
            ) : (
              // Single-row scrollable navigation
              <nav className="desktop-nav" ref={navRef}>
                {menuItems.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`desktop-nav-item ${location.pathname === item.path ? 'active' : ''}`}
                  >
                    <span className="nav-icon">{item.icon}</span>
                    <span className="nav-label">{item.label}</span>
                  </Link>
                ))}
              </nav>
            )}

            {/* Navigation Layout Toggle */}
            <button
              onClick={() => setUseDoubleRowNav(!useDoubleRowNav)}
              className="nav-toggle-btn"
              title={useDoubleRowNav ? 'Switch to sliding navigation' : 'Switch to double-row navigation'}
            >
              {useDoubleRowNav ? '⬌' : '⬍'}
            </button>
          </div>
        </div>

        <div className="header-right">
          <div className="user-info">
            <span className="user-name">
              {user?.firstName} {user?.lastName}
            </span>
            <span className="user-role">{user?.role}</span>
          </div>

          <div className="user-menu">
            <Link to="/profile" className="profile-link">
              Profile
            </Link>
            <button onClick={handleLogout} className="logout-button">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Sidebar */}
      <aside className={`sidebar ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <nav className="sidebar-nav">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}
              onClick={() => setSidebarOpen(false)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </Link>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main className={`main-content ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <div className="content-wrapper">
          <Outlet />
        </div>
      </main>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div className="sidebar-overlay" onClick={() => setSidebarOpen(false)} />
      )}
    </div>
  );
};

export default Layout;
