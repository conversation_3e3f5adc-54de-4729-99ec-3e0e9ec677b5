.assignments {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.assignments-header {
  text-align: center;
  margin-bottom: 2rem;
}

.assignments-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.assignments-header p {
  font-size: 1.1rem;
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
}

.assignments-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Search functionality styles */
.search-container {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #fff;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-input::placeholder {
  color: #95a5a6;
  font-style: italic;
}

.search-results-count {
  margin-top: 0.5rem;
  margin-bottom: 0;
  font-size: 0.9rem;
  color: #7f8c8d;
  font-style: italic;
}

.assignments-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-section {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.assignments-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #7f8c8d;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  background: white;
  font-size: 0.875rem;
  color: #2c3e50;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Download button styling */
.btn-success {
  background-color: #27ae60;
  color: white;
  border: 1px solid #27ae60;
}

.btn-success:hover {
  background-color: #229954;
  border-color: #229954;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e1e8ed;
  background: white;
  color: #7f8c8d;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

.assignments-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.assignments-table {
  width: 100%;
  border-collapse: collapse;
}

.assignments-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  white-space: nowrap;
}

.assignments-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.assignment-row:hover {
  background: #f8f9fa;
}

.assignment-title .title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.assignment-title .title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.assignment-title .description {
  font-size: 0.875rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.group {
  background: #e8f5e8;
  color: #27ae60;
}

.type-badge.individual {
  background: #e8f4fd;
  color: #3498db;
}

.assigned-to {
  font-size: 0.9rem;
}

.group-assignment {
  color: #27ae60;
  font-weight: 500;
}

.individual-assignment {
  color: #3498db;
  font-weight: 500;
}

.computer-info {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.scheduled-date {
  font-size: 0.9rem;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.assignment-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-outline {
  background: white;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.no-assignments {
  text-align: center;
  padding: 3rem;
}

.no-data h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.no-data p {
  color: #7f8c8d;
}

.assignments-summary {
  text-align: center;
  padding: 1rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Create Assignment Section */
.create-assignment-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.create-assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.create-assignment-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.25rem;
}

.create-assignment-note {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
}

.create-assignment-note strong {
  color: #495057;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .assignments-table th,
  .assignments-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }
  
  .assignment-title .description {
    display: none;
  }
}

@media (max-width: 768px) {
  .assignments {
    padding: 1rem;
  }
  
  .assignments-controls {
    padding: 1rem;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-tabs {
    justify-content: center;
  }
  
  .assignments-table-container {
    overflow-x: auto;
  }
  
  .assignments-table {
    min-width: 800px;
  }
  
  .assignment-actions {
    flex-direction: column;
  }

  .admin-selectors {
    flex-direction: column;
    gap: 1rem;
  }

  .seats-grid,
  .computers-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .admin-tabs {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .assignments-header h1 {
    font-size: 2rem;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
  }
  
  .assignments-table {
    min-width: 600px;
  }
}

/* Admin Interface Styles */
.admin-interface {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.admin-controls h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.25rem;
}

.admin-selectors {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.selector-group {
  flex: 1;
}

.selector-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.form-select:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.admin-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background: #f8f9fa;
}

.tab-button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* Seats Management */
.seats-management h4,
.computers-management h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.seats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.seat-item {
  padding: 1rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  text-align: center;
  transition: all 0.2s ease;
}

.seat-item.available {
  background: #f8f9fa;
  border-color: #6c757d;
}

.seat-item.assigned {
  background: #d4edda;
  border-color: #28a745;
}

.seat-number {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}

.seat-assignment {
  font-size: 0.875rem;
}

.student-name {
  font-weight: 500;
  color: #28a745;
}

.student-id {
  color: #6c757d;
  font-size: 0.75rem;
}

/* Computers Management */
.computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.computer-item {
  padding: 1rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.computer-item.available {
  background: #f8f9fa;
  border-color: #6c757d;
}

.computer-item.assigned {
  background: #d4edda;
  border-color: #28a745;
}

.computer-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}

.computer-status {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.computer-assignment {
  font-size: 0.875rem;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.assignment-status-info {
  font-weight: 500;
  color: #28a745;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.assignment-detail-modal {
  width: 800px;
}

.create-assignment-modal {
  width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.modal-content {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 0 0 12px 12px;
  flex-shrink: 0;
}

/* Assignment Detail Modal Styles */
.assignment-detail-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-section {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.info-item span {
  color: #2c3e50;
  font-size: 1rem;
}

.info-item .status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  background-color: #3498db;
  color: white;
  width: fit-content;
}

/* Create Assignment Modal Styles */
.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.assignment-type-selector {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.radio-option:hover {
  border-color: #3498db;
  background-color: #f8f9fa;
}

.radio-option input[type="radio"]:checked + span {
  color: #3498db;
  font-weight: 600;
}

.radio-option input[type="radio"] {
  margin: 0;
}

.selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.checkbox-option:hover {
  background-color: #e9ecef;
}

.checkbox-option input[type="checkbox"] {
  margin: 0;
}

.checkbox-option span {
  font-size: 0.9rem;
  color: #495057;
}

.no-data-message {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .assignment-detail-modal,
  .create-assignment-modal {
    width: 100%;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .assignment-type-selector {
    flex-direction: column;
  }

  .selection-grid {
    grid-template-columns: 1fr;
  }
}

/* File Upload Styling */
.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s ease;
}

.file-upload-area:hover {
  border-color: #007bff;
}

.file-upload-label {
  display: block;
  cursor: pointer;
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.file-upload-label:hover {
  background-color: #f8f9fa;
}

.file-selected {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #28a745;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #6c757d;
}

.file-icon, .upload-icon {
  font-size: 24px;
}

.file-name {
  font-weight: 500;
  color: #333;
}

.file-size {
  color: #6c757d;
  font-size: 0.9em;
}

/* View Toggle Styles */
.view-toggle {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #495057;
  margin: 0;
}

.toggle-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.toggle-text {
  font-weight: 500;
}

/* Consolidated View Styles */
.consolidated-assignment {
  font-size: 0.9rem;
  line-height: 1.4;
  max-width: 200px;
  word-wrap: break-word;
}

.deadline-date {
  color: #dc3545;
  font-weight: 500;
}

/* File Download Styles */
.file-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.file-name {
  font-weight: 500;
  color: #495057;
}

.file-size {
  font-size: 0.8rem;
  color: #6c757d;
}

/* Assignment Creation Specific Styles */
.assignment-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.assignment-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.download-btn {
  align-self: flex-start;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.download-icon {
  font-size: 0.875rem;
  line-height: 1;
}

.no-file {
  color: #999;
  font-style: italic;
}

/* Enhanced Status Badge Styles */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  text-align: center;
  min-width: 70px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Status-specific colors */
.status-badge.published {
  background-color: #28a745;
  border: 1px solid #1e7e34;
}

.status-badge.draft {
  background-color: #dc3545;
  border: 1px solid #c82333;
}

.status-badge.archived {
  background-color: #fd7e14;
  border: 1px solid #e55a00;
}

/* Assignment Distribution Status Badge Colors */
.status-badge.status-assigned {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffc107;
}

.status-badge.status-in_progress {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #28a745;
}

.status-badge.status-completed {
  background-color: #ffeaa7;
  color: #e65100;
  border: 1px solid #ff9800;
}

.status-badge.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #dc3545;
}

.status-badge.status-unknown {
  background-color: #e2e3e5;
  color: #6c757d;
  border: 1px solid #ced4da;
}

/* Type Badge Styles */
.type-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.type-badge.class {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.type-badge.group {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #e1bee7;
}

.type-badge.individual {
  background-color: #e8f5e8;
  color: #388e3c;
  border: 1px solid #c8e6c9;
}

/* Assignment Actions Improvements */
.assignment-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.assignment-actions .btn {
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 4px;
  border: 1px solid;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.assignment-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assignment-actions .btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.assignment-actions .btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

.assignment-actions .btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.assignment-actions .btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

.assignment-actions .btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.assignment-actions .btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

/* Create Assignment Section */
.create-assignment-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.create-assignment-section .btn {
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
}

/* Form Row for Date Fields */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Delete Confirmation Modal */
.delete-confirm-modal {
  max-width: 400px;
}

.delete-confirm-modal .modal-content {
  text-align: center;
  padding: 1.5rem;
}

.delete-confirm-modal .modal-content p {
  margin-bottom: 1rem;
  font-size: 1rem;
}

.delete-confirm-modal .modal-content p:last-child {
  color: #dc3545;
  font-weight: 600;
  margin-bottom: 0;
}

.delete-confirm-modal .modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 1rem 1.5rem;
}

.delete-confirm-modal .btn {
  min-width: 100px;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .assignment-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .assignment-actions .btn {
    width: 100%;
    min-width: auto;
  }

  .delete-confirm-modal .modal-footer {
    flex-direction: column;
  }

  .delete-confirm-modal .btn {
    width: 100%;
  }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .view-toggle {
    margin-left: 0;
    margin-top: 1rem;
  }

  .file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}

/* Upcoming assignment styles */
.upcoming-badge {
  font-size: 0.75rem;
  color: #856404;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 0.125rem 0.5rem;
  margin-top: 0.25rem;
  display: inline-block;
  font-weight: 500;
}

.restricted-file {
  color: #dc3545;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: help;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.restricted-file:hover {
  color: #a71e2a;
}

/* Upcoming status badge override */
.status-badge:contains("UPCOMING") {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffc107;
}
