.student-dashboard {
  padding: 0;
  background: #f8fafc;
  min-height: 100vh;
}

.student-dashboard.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  font-size: 1.2rem;
  color: #667eea;
  text-align: center;
}

/* Dashboard Header */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  margin-bottom: 2rem;
}

.welcome-section h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.welcome-section p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
}

.student-info {
  display: flex;
  gap: 2rem;
  font-size: 0.95rem;
  opacity: 0.8;
}

.student-id, .group-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Stats Section */
.stats-section {
  margin: 0 2rem 2rem 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.primary { border-left: 4px solid #667eea; }
.stat-card.warning { border-left: 4px solid #f59e0b; }
.stat-card.success { border-left: 4px solid #10b981; }
.stat-card.info { border-left: 4px solid #3b82f6; }

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #1a202c;
}

.stat-content p {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 0 2rem;
}

.dashboard-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.view-all-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-all-btn:hover {
  background: #5a67d8;
}

/* Assignments List */
.assignments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.assignment-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  transition: border-color 0.2s;
}

.assignment-item:hover {
  border-color: #667eea;
}

.assignment-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.assignment-type {
  font-size: 1.2rem;
}

.assignment-header h4 {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.assignment-status {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.assignment-description {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  line-height: 1.4;
}

.assignment-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #64748b;
}

/* Grades List */
.grades-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.grade-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.grade-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.grade-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.score {
  font-size: 1.2rem;
  font-weight: 700;
  color: #667eea;
}

.percentage {
  font-size: 0.9rem;
  color: #64748b;
}

.grade-date {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

.grade-feedback {
  font-size: 0.9rem;
  color: #374151;
  margin: 0.5rem 0 0 0;
  font-style: italic;
}

/* Group Card */
.group-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.group-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.group-class {
  background: #f1f5f9;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.leader-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.group-members h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.member-name {
  font-weight: 500;
  color: #1a202c;
}

.member-id {
  color: #64748b;
}

.member-role {
  font-size: 1rem;
}

.more-members {
  font-size: 0.8rem;
  color: #64748b;
  font-style: italic;
}

/* Quick Actions */
.quick-actions-section {
  margin: 2rem;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quick-actions-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1.5rem 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-btn {
  padding: 1rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.primary:hover {
  background: #5a67d8;
}

.action-btn.secondary {
  background: #10b981;
  color: white;
}

.action-btn.secondary:hover {
  background: #059669;
}

.action-btn.tertiary {
  background: #f59e0b;
  color: white;
}

.action-btn.tertiary:hover {
  background: #d97706;
}

.action-btn.quaternary {
  background: #6b7280;
  color: white;
}

.action-btn.quaternary:hover {
  background: #4b5563;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.empty-state p {
  margin: 0;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .student-dashboard {
    padding: 0;
  }
  
  .dashboard-header {
    padding: 1.5rem;
  }
  
  .welcome-section h1 {
    font-size: 2rem;
  }
  
  .student-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stats-section,
  .dashboard-content,
  .quick-actions-section {
    margin: 0 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .assignment-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Seat Assignments */
.seat-assignments {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.seat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.seat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.seat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.seat-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
}

.seat-icon {
  font-size: 1.2rem;
}

.seat-text {
  font-size: 1.1rem;
}

.lab-name {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.seat-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.schedule-title {
  font-weight: 600;
  color: #374151;
  margin: 0;
  font-size: 1rem;
}

.seat-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.seat-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.lab-location {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  font-style: italic;
}

.more-seats {
  text-align: center;
  padding: 1rem;
  color: #6b7280;
  font-style: italic;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 8px;
}
