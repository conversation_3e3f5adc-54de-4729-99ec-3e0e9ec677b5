import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import { submissionsAPI } from '../../services/api';
import './StudentSubmissions.css';

interface Assignment {
  id: string;
  assignmentId: string;
  title: string;
  description: string;
  dueDate: string;
  scheduledDate: string;
  scheduledDateFormatted?: string;
  status: string;
  assignmentStatus: string;
  assignmentType: string;
  className: string;
  instructorName: string;
  assignedAt: string;
  groupName?: string;
  type: 'individual' | 'group' | 'class';
  canAccessPdf?: boolean;
  canUpload?: boolean;
  isUpcoming?: boolean;
  pdfFileName?: string;
  submission?: {
    id: string | null;
    assignmentResponseFilename: string | null;
    outputTestFilename: string | null;
    submittedAt: string | null;
    isLocked: boolean;
    hasResponse: boolean;
    hasOutput: boolean;
    isComplete: boolean;
  };
}

interface Submission {
  id: string;
  scheduleId: string;
  title: string;
  submittedAt: string;
  status: string;
  files?: Array<{
    id: string;
    originalFilename: string;
    fileType: string;
    fileSize: number;
  }>;
}

interface UploadModalData {
  assignment: Assignment;
  fileType: 'assignment_response' | 'output_test';
}

const StudentSubmissions: React.FC = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadModal, setUploadModal] = useState<{
    isOpen: boolean;
    data: UploadModalData | null;
  }>({
    isOpen: false,
    data: null
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState<'assignments' | 'history'>('assignments');

  useEffect(() => {
    fetchAssignments();
    fetchSubmissions();
  }, []);

  const fetchAssignments = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/assignments/student', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
    }
  };

  const fetchSubmissions = async () => {
    try {
      const response = await submissionsAPI.getSubmissions();
      if (response.data?.submissions) {
        setSubmissions(response.data.submissions);
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSubmissionForAssignment = (scheduleId: string) => {
    return submissions.find(sub => sub.scheduleId === scheduleId);
  };

  const openUploadModal = (assignment: Assignment, fileType: 'assignment_response' | 'output_test') => {
    setUploadModal({
      isOpen: true,
      data: { assignment, fileType }
    });
  };

  const submitAssignment = async (assignment: Assignment) => {
    if (!assignment.submission?.hasResponse || !assignment.submission?.hasOutput) {
      showNotification('Please upload both assignment response and output test files before submitting.', 'error');
      return;
    }

    try {
      const formData = new FormData();

      // Note: In a real implementation, you would need to get the actual files
      // For now, we'll assume the files are already uploaded and we're just marking as submitted

      const response = await fetch(`/api/assignments/submit/${assignment.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (response.ok) {
        showNotification('Assignment submitted successfully!', 'success');
        fetchAssignments(); // Refresh the assignments list
      } else {
        const error = await response.json();
        showNotification(error.error || 'Failed to submit assignment', 'error');
      }
    } catch (error) {
      console.error('Error submitting assignment:', error);
      showNotification('Failed to submit assignment', 'error');
    }
  };

  const closeUploadModal = () => {
    setUploadModal({ isOpen: false, data: null });
    setSelectedFile(null);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        showError('File size must be less than 10MB');
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !uploadModal.data) return;

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('scheduleId', uploadModal.data.assignment.scheduleId);
      formData.append('fileType', uploadModal.data.fileType);

      const token = localStorage.getItem('token');
      const response = await fetch('/api/submissions/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        showSuccess('File uploaded successfully');
        closeUploadModal();
        fetchSubmissions(); // Refresh submissions
      } else {
        const errorData = await response.json();
        showError(errorData.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      showError('Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const downloadFile = async (submissionId: string, filename: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/submissions/${submissionId}/files/${filename}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showSuccess('File downloaded successfully');
      } else {
        showError('Failed to download file');
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      showError('Failed to download file');
    }
  };

  if (loading) {
    return (
      <div className="student-submissions loading">
        <div className="loading-spinner">Loading assignments...</div>
      </div>
    );
  }

  return (
    <div className="student-submissions">
      <div className="submissions-header">
        <h1>My Assignments</h1>
        <p>Upload your assignment responses and view submission history</p>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button 
          className={`tab-button ${activeTab === 'assignments' ? 'active' : ''}`}
          onClick={() => setActiveTab('assignments')}
        >
          📝 Current Assignments
        </button>
        <button 
          className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          📚 Submission History
        </button>
      </div>

      {/* Current Assignments Tab */}
      {activeTab === 'assignments' && (
        <div className="assignments-grid">
          {assignments.map((assignment) => {
            const getStatusBadgeClass = (status: string) => {
              switch (status) {
                case 'completed': return 'status-completed';
                case 'in_progress': return 'status-in-progress';
                case 'cancelled': return 'status-cancelled';
                case 'upcoming': return 'status-upcoming';
                default: return 'status-default';
              }
            };

            const getStatusIcon = (status: string) => {
              switch (status) {
                case 'completed': return '✅';
                case 'in_progress': return '⏳';
                case 'cancelled': return '❌';
                case 'upcoming': return '📅';
                default: return '📋';
              }
            };

            return (
              <div key={assignment.id} className="assignment-card">
                <div className="assignment-header">
                  <div className="assignment-title-section">
                    <span className="assignment-type-icon">
                      {assignment.assignmentType === 'individual' ? '👤' : assignment.assignmentType === 'group' ? '👥' : '🏫'}
                    </span>
                    <h3>{assignment.title}</h3>
                    <span className="assignment-type-badge">{assignment.assignmentType}</span>
                    {assignment.groupName && (
                      <span className="group-badge">👥 {assignment.groupName}</span>
                    )}
                  </div>
                  <span className={`assignment-status ${getStatusBadgeClass(assignment.status)}`}>
                    {getStatusIcon(assignment.status)} {assignment.status.toUpperCase()}
                  </span>
                </div>

                <div className="assignment-details">
                  <p className="description">{assignment.description}</p>
                  {assignment.status === 'upcoming' && assignment.scheduledDateFormatted && (
                    <div className="upcoming-notice">
                      <span className="upcoming-text">
                        📅 Available on: {assignment.scheduledDateFormatted}
                      </span>
                      <span className="upcoming-description">
                        Assignment content will be accessible on the scheduled date.
                      </span>
                    </div>
                  )}
                  <div className="assignment-meta">
                    <span className="class">👥 {assignment.className}</span>
                    <span className="instructor">👨‍🏫 {assignment.instructorName}</span>
                    <span className="date">📅 Due: {new Date(assignment.dueDate).toLocaleDateString()}</span>
                    {assignment.submission?.submittedAt && (
                      <span className="submitted">✅ Submitted: {new Date(assignment.submission.submittedAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>

                <div className="submission-section">
                  <h4>Submission Status</h4>

                  {assignment.status === 'upcoming' ? (
                    <div className="upcoming-restriction">
                      <div className="restriction-notice">
                        <span className="restriction-icon">🔒</span>
                        <div className="restriction-text">
                          <p><strong>Assignment Not Yet Available</strong></p>
                          <p>This assignment will be available for submission on {assignment.scheduledDateFormatted}.</p>
                          <p>Please check back after the scheduled date to access the assignment content and submit your work.</p>
                        </div>
                      </div>
                    </div>
                  ) : assignment.status === 'cancelled' ? (
                    <div className="cancelled-restriction">
                      <div className="restriction-notice">
                        <span className="restriction-icon">❌</span>
                        <div className="restriction-text">
                          <p><strong>Assignment Deadline Passed</strong></p>
                          <p>The deadline for this assignment has passed and submissions are no longer accepted.</p>
                        </div>
                      </div>
                    </div>
                  ) : assignment.status === 'completed' ? (
                    <div className="completed-submission">
                      <div className="completion-notice">
                        <span className="completion-icon">✅</span>
                        <div className="completion-text">
                          <p><strong>Assignment Completed</strong></p>
                          <p>Submitted on: {assignment.submission?.submittedAt ? new Date(assignment.submission.submittedAt).toLocaleString() : 'Unknown'}</p>
                        </div>
                      </div>
                      <div className="submitted-files">
                        {assignment.submission?.assignmentResponseFilename && (
                          <div className="submitted-file">
                            <span className="file-icon">📄</span>
                            <span className="file-label">Response:</span>
                            <span className="file-name">{assignment.submission.assignmentResponseFilename}</span>
                          </div>
                        )}
                        {assignment.submission?.outputTestFilename && (
                          <div className="submitted-file">
                            <span className="file-icon">🧪</span>
                            <span className="file-label">Output:</span>
                            <span className="file-name">{assignment.submission.outputTestFilename}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="file-upload-section">
                      <div className="upload-item">
                        <div className="upload-header">
                          <span className="upload-label">📄 Assignment Response</span>
                          {assignment.submission?.hasResponse ? (
                            <span className="file-status uploaded">✅ Uploaded</span>
                          ) : (
                            <span className="file-status pending">⏳ Pending</span>
                          )}
                        </div>

                        {assignment.submission?.assignmentResponseFilename ? (
                          <div className="uploaded-file">
                            <span className="filename">{assignment.submission.assignmentResponseFilename}</span>
                          </div>
                        ) : assignment.canUpload ? (
                          <button
                            className="upload-btn"
                            onClick={() => openUploadModal(assignment, 'assignment_response')}
                          >
                            📤 Upload Response
                          </button>
                        ) : (
                          <span className="upload-disabled">Upload not available</span>
                        )}
                      </div>

                      <div className="upload-item">
                        <div className="upload-header">
                          <span className="upload-label">🧪 Output Test</span>
                          {assignment.submission?.hasOutput ? (
                            <span className="file-status uploaded">✅ Uploaded</span>
                          ) : (
                            <span className="file-status pending">⏳ Pending</span>
                          )}
                        </div>

                        {assignment.submission?.outputTestFilename ? (
                          <div className="uploaded-file">
                            <span className="filename">{assignment.submission.outputTestFilename}</span>
                          </div>
                        ) : assignment.canUpload ? (
                          <button
                            className="upload-btn"
                            onClick={() => openUploadModal(assignment, 'output_test')}
                          >
                            📤 Upload Output Test
                          </button>
                        ) : (
                          <span className="upload-disabled">Upload not available</span>
                        )}
                      </div>

                      {assignment.canUpload && assignment.submission?.hasResponse && assignment.submission?.hasOutput && (
                        <div className="submit-assignment">
                          <button
                            className="submit-btn"
                            onClick={() => submitAssignment(assignment)}
                          >
                            🚀 Submit Assignment
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {assignments.length === 0 && (
            <div className="no-assignments">
              <h3>No assignments found</h3>
              <p>You don't have any assignments yet.</p>
            </div>
          )}
        </div>
      )}

      {/* Submission History Tab */}
      {activeTab === 'history' && (
        <div className="submissions-history">
          <div className="history-header">
            <h2>Submission History</h2>
            <p>View all your past submissions and their status</p>
          </div>
          
          <div className="history-list">
            {submissions.length > 0 ? (
              submissions.map((submission) => {
                const assignment = assignments.find(a => a.scheduleId === submission.scheduleId);
                return (
                  <div key={submission.id} className="history-item">
                    <div className="history-header">
                      <div className="submission-info">
                        <h3>{submission.title}</h3>
                        <p className="assignment-title">
                          Assignment: {assignment?.title || 'Unknown Assignment'}
                        </p>
                        <div className="submission-meta">
                          <span>📅 Submitted: {new Date(submission.submittedAt).toLocaleDateString()}</span>
                          <span>📍 {assignment?.labName || 'N/A'}</span>
                          <span>👥 {assignment?.className || 'N/A'}</span>
                        </div>
                      </div>
                      <div className="submission-status">
                        <span className={`status-badge ${submission.status}`}>
                          {submission.status}
                        </span>
                      </div>
                    </div>
                    
                    {submission.files && submission.files.length > 0 && (
                      <div className="submission-files">
                        <h4>Submitted Files:</h4>
                        <div className="files-list">
                          {submission.files.map((file) => (
                            <div key={file.id} className="file-item">
                              <div className="file-info">
                                <span className="file-icon">📄</span>
                                <div className="file-details">
                                  <span className="filename">{file.originalFilename}</span>
                                  <span className="file-meta">
                                    {file.fileType.replace('_', ' ')} • {formatFileSize(file.fileSize)}
                                  </span>
                                </div>
                              </div>
                              <button 
                                className="download-btn"
                                onClick={() => downloadFile(submission.id, file.originalFilename)}
                              >
                                📥 Download
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="no-history">
                <div className="no-history-icon">📚</div>
                <h3>No Submission History</h3>
                <p>You haven't submitted any assignments yet. Complete assignments to see your submission history here.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {uploadModal.isOpen && uploadModal.data && (
        <div className="modal-overlay" onClick={closeUploadModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Upload {uploadModal.data.fileType.replace('_', ' ')}</h3>
              <button className="close-btn" onClick={closeUploadModal}>×</button>
            </div>
            
            <div className="modal-body">
              <p><strong>Assignment:</strong> {uploadModal.data.assignment.title}</p>
              <p><strong>File Type:</strong> {uploadModal.data.fileType.replace('_', ' ')}</p>
              
              <div className="file-input-section">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt,.zip"
                  onChange={handleFileSelect}
                  className="file-input"
                />
                {selectedFile && (
                  <div className="selected-file">
                    <span>Selected: {selectedFile.name}</span>
                    <span>Size: {formatFileSize(selectedFile.size)}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="cancel-btn" onClick={closeUploadModal}>Cancel</button>
              <button 
                className="upload-btn" 
                onClick={handleUpload}
                disabled={!selectedFile || uploading}
              >
                {uploading ? 'Uploading...' : 'Upload'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentSubmissions;
