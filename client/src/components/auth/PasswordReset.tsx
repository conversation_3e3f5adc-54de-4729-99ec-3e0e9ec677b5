import React, { useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import { authAPI } from '../../services/api';
import './Auth.css';

const PasswordReset: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();
  
  const token = searchParams.get('token');
  const isResetMode = !!token;

  const [formData, setFormData] = useState({
    email: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email) {
      showError('Validation Error', 'Please enter your email address');
      return;
    }

    setLoading(true);

    try {
      await authAPI.requestPasswordReset(formData.email);
      showSuccess(
        'Reset Link Sent', 
        'If the email exists in our system, you will receive a password reset link shortly.'
      );
      setFormData({ email: '', newPassword: '', confirmPassword: '' });
    } catch (error: any) {
      showError('Request Failed', error.response?.data?.error || 'Failed to send reset link');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.newPassword || !formData.confirmPassword) {
      showError('Validation Error', 'Please fill in all fields');
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      showError('Password Mismatch', 'Passwords do not match');
      return;
    }

    if (formData.newPassword.length < 6) {
      showError('Invalid Password', 'Password must be at least 6 characters long');
      return;
    }

    setLoading(true);

    try {
      await authAPI.resetPassword({
        token: token!,
        newPassword: formData.newPassword
      });
      
      showSuccess(
        'Password Reset Successful', 
        'Your password has been reset. You can now log in with your new password.'
      );
      
      navigate('/login');
    } catch (error: any) {
      showError('Reset Failed', error.response?.data?.error || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>LabSyncPro</h1>
          <h2>{isResetMode ? 'Reset Password' : 'Forgot Password'}</h2>
          <p>
            {isResetMode 
              ? 'Enter your new password below.' 
              : 'Enter your email address and we\'ll send you a link to reset your password.'
            }
          </p>
        </div>

        <form onSubmit={isResetMode ? handleResetPassword : handleRequestReset} className="auth-form">
          {!isResetMode ? (
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email address"
              />
            </div>
          ) : (
            <>
              <div className="form-group">
                <label htmlFor="newPassword">New Password</label>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleChange}
                  required
                  placeholder="Enter your new password"
                  minLength={6}
                />
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm New Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  placeholder="Confirm your new password"
                  minLength={6}
                />
              </div>
            </>
          )}

          <button
            type="submit"
            className="auth-button"
            disabled={loading}
          >
            {loading 
              ? (isResetMode ? 'Resetting Password...' : 'Sending Reset Link...') 
              : (isResetMode ? 'Reset Password' : 'Send Reset Link')
            }
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Remember your password? <Link to="/login">Sign In</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;
