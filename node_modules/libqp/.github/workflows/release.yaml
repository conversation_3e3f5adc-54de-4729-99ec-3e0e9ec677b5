on:
    push:
        branches:
            - master

permissions:
    contents: write
    pull-requests: write
    id-token: write

name: release
jobs:
    release-please:
        runs-on: ubuntu-latest
        steps:
            - uses: google-github-actions/release-please-action@v3
              id: release
              with:
                  release-type: node
                  package-name: ${{vars.NPM_MODULE_NAME}}
                  pull-request-title-pattern: 'chore${scope}: release ${version} [skip-ci]'
            # The logic below handles the npm publication:
            - uses: actions/checkout@v4
              # these if statements ensure that a publication only occurs when
              # a new release is created:
              if: ${{ steps.release.outputs.release_created }}
            - uses: actions/setup-node@v4
              with:
                  node-version: 20
                  registry-url: 'https://registry.npmjs.org'
              if: ${{ steps.release.outputs.release_created }}
            - run: npm ci
              if: ${{ steps.release.outputs.release_created }}
            - run: npm publish --provenance --access public
              env:
                  NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
              if: ${{ steps.release.outputs.release_created }}
