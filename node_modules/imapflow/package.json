{"name": "imapflow", "version": "1.0.189", "description": "IMAP Client for Node", "main": "lib/imap-flow.js", "types": "lib/imap-flow.d.ts", "scripts": {"test": "grunt", "prepare": "npm run build", "docs": "rm -rf docs && mkdir -p docs && jsdoc lib/imap-flow.js -c jsdoc.json -R README.md --destination docs/ && cp assets/favicon.ico docs", "build": "npm run docs", "st": "npm run docs && st -d docs -i index.html", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "repository": {"type": "git", "url": "git+https://github.com/postalsys/imapflow.git"}, "keywords": ["imap", "email", "mail"], "author": "Postal Systems OÜ", "license": "MIT", "bugs": {"url": "https://github.com/postalsys/imapflow/issues"}, "homepage": "https://imapflow.com/", "devDependencies": {"@babel/eslint-parser": "7.27.5", "@babel/eslint-plugin": "7.27.1", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/preset-env": "7.27.2", "@types/node": "24.0.7", "eslint": "8.57.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "grunt": "1.6.1", "grunt-cli": "1.5.0", "grunt-contrib-nodeunit": "5.0.0", "grunt-eslint": "24.3.0", "imapflow-jsdoc-template": "3.4.0-imapflow.2", "jsdoc": "3.6.11", "st": "3.0.2", "typescript": "5.8.3"}, "dependencies": {"encoding-japanese": "2.2.0", "iconv-lite": "0.6.3", "libbase64": "1.3.0", "libmime": "5.3.7", "libqp": "2.1.1", "mailsplit": "5.4.5", "nodemailer": "7.0.4", "pino": "9.7.0", "socks": "2.8.5"}}