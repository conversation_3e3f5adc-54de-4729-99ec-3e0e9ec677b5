{"name": "mailsplit", "version": "5.4.5", "description": "Split email messages into an object stream", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "grunt", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "author": "<PERSON><PERSON>", "license": "(MIT OR EUPL-1.1+)", "dependencies": {"libbase64": "1.3.0", "libmime": "5.3.7", "libqp": "2.1.1"}, "devDependencies": {"eslint": "8.29.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "grunt": "1.6.1", "grunt-cli": "1.5.0", "grunt-contrib-nodeunit": "5.0.0", "grunt-eslint": "24.0.1", "random-message": "1.1.0"}, "files": ["lib", "index.js"], "repository": {"type": "git", "url": "https://github.com/zone-eu/mailsplit.git"}}