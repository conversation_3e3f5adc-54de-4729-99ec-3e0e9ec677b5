'use strict';

// https://nodejs.org/api/util.html#encodings-supported-by-default-with-full-icu-data
// NB! iso-8859-16 not supported
const CHARSETS = [
    '866',
    'ansi_x3.4-1968',
    'arabic',
    'ascii',
    'asmo-708',
    'big5',
    'big5-hkscs',
    'chinese',
    'cn-big5',
    'cp1250',
    'cp1251',
    'cp1252',
    'cp1253',
    'cp1254',
    'cp1255',
    'cp1256',
    'cp1257',
    'cp1258',
    'cp819',
    'cp866',
    'csbig5',
    'cseuckr',
    'cseucpkdfmtjapanese',
    'csgb2312',
    'csibm866',
    'csiso2022jp',
    'csiso58gb231280',
    'csiso88596e',
    'csiso88596i',
    'csiso88598e',
    'csiso88598i',
    'csisolatin1',
    'csisolatin2',
    'csisolatin3',
    'csisolatin4',
    'csisolatin5',
    'csisolatin6',
    'csisolatin9',
    'csisolatinarabic',
    'csisolatincyrillic',
    'csisolatingreek',
    'csisolatinhebrew',
    'cskoi8r',
    'csksc56011987',
    'csmacintosh',
    'csshiftjis',
    'cyrillic',
    'dos-874',
    'ecma-114',
    'ecma-118',
    'elot_928',
    'euc-jp',
    'euc-kr',
    'gb18030',
    'gb2312',
    'gb_2312',
    'gb_2312-80',
    'gbk',
    'greek',
    'greek8',
    'hebrew',
    'ibm819',
    'ibm866',
    'iso-2022-jp',
    'iso-8859-1',
    'iso8859-1',
    'iso88591',
    'iso_8859-1',
    'iso_8859-1:1987',
    'iso-8859-10',
    'iso8859-10',
    'iso885910',
    'iso-8859-11',
    'iso8859-11',
    'iso885911',
    'iso-8859-13',
    'iso8859-13',
    'iso885913',
    'iso-8859-14',
    'iso8859-14',
    'iso885914',
    'iso-8859-15',
    'iso8859-15',
    'iso885915',
    'iso_8859-15',
    'iso-8859-2',
    'iso8859-2',
    'iso88592',
    'iso_8859-2',
    'iso_8859-2:1987',
    'iso-8859-3',
    'iso8859-3',
    'iso88593',
    'iso_8859-3',
    'iso_8859-3:1988',
    'iso-8859-4',
    'iso8859-4',
    'iso88594',
    'iso_8859-4',
    'iso_8859-4:1988',
    'iso-8859-5',
    'iso8859-5',
    'iso88595',
    'iso_8859-5',
    'iso_8859-5:1988',
    'iso-8859-6',
    'iso8859-6',
    'iso88596',
    'iso_8859-6',
    'iso_8859-6:1987',
    'iso-8859-6-e',
    'iso-8859-6-i',
    'iso-8859-7',
    'iso8859-7',
    'iso88597',
    'iso_8859-7',
    'iso_8859-7:1987',
    'iso-8859-8',
    'iso8859-8',
    'iso88598',
    'iso_8859-8',
    'iso_8859-8:1988',
    'iso-8859-8-e',
    'iso-8859-8-i',
    'iso-8859-9',
    'iso8859-9',
    'iso88599',
    'iso_8859-9',
    'iso_8859-9:1989',
    'iso-ir-100',
    'iso-ir-101',
    'iso-ir-109',
    'iso-ir-110',
    'iso-ir-126',
    'iso-ir-127',
    'iso-ir-138',
    'iso-ir-144',
    'iso-ir-148',
    'iso-ir-149',
    'iso-ir-157',
    'iso-ir-58',
    'koi',
    'koi8',
    'koi8-r',
    'koi8_r',
    'koi8-ru',
    'koi8-u',
    'korean',
    'ksc5601',
    'ksc_5601',
    'ks_c_5601-1987',
    'ks_c_5601-1989',
    'l1',
    'l2',
    'l3',
    'l4',
    'l5',
    'l6',
    'l9',
    'latin1',
    'latin2',
    'latin3',
    'latin4',
    'latin5',
    'latin6',
    'logical',
    'mac',
    'macintosh',
    'ms932',
    'ms_kanji',
    'shift_jis',
    'shift-jis',
    'sjis',
    'sun_eu_greek',
    'tis-620',
    'unicode-1-1-utf-8',
    'us-ascii',
    'utf-16',
    'utf-16be',
    'utf-16le',
    'utf-8',
    'utf8',
    'visual',
    'windows-1250',
    'windows-1251',
    'windows-1252',
    'windows-1253',
    'windows-1254',
    'windows-1255',
    'windows-1256',
    'windows-1257',
    'windows-1258',
    'windows-31j',
    'windows-874',
    'windows-949',
    'x-cp1250',
    'x-cp1251',
    'x-cp1252',
    'x-cp1253',
    'x-cp1254',
    'x-cp1255',
    'x-cp1256',
    'x-cp1257',
    'x-cp1258',
    'x-euc-jp',
    'x-gbk',
    'x-mac-cyrillic',
    'x-mac-roman',
    'x-mac-ukrainian',
    'x-sjis',
    'x-x-big5'
];

const CHARSET_MAP = new Map(CHARSETS.map(charset => [charset.replace(/[-_]+/g, ''), charset]));

const getCharsetName = charset => {
    let charsetKey = (charset || '').toString().toLowerCase().trim().replace(/[-_]+/g, '');

    return CHARSET_MAP.get(charsetKey) || false;
};

module.exports.getCharsetName = getCharsetName;
