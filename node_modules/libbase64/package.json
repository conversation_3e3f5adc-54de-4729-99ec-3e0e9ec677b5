{"name": "libbase64", "version": "1.3.0", "description": "Encode and decode base64 encoded strings", "main": "lib/libbase64.js", "scripts": {"test": "grunt", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "repository": {"type": "git", "url": "git://github.com/nodemailer/libbase64.git"}, "keywords": ["base64", "mime"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nodemailer/libbase64/issues"}, "homepage": "https://github.com/nodemailer/libbase64", "devDependencies": {"chai": "4.4.1", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "grunt": "1.6.1", "grunt-cli": "1.4.3", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "mocha": "10.3.0"}, "dependencies": {}}